../../../bin/mlx_lm.awq,sha256=DlX8CKQP4U2UsdHwPNYw6_5KA8KLpSj72-BG9ao_iAE,303
../../../bin/mlx_lm.cache_prompt,sha256=V7dKB7Xca3gm6x2iEZwHJmTTMybrwRDcDBSiduggsXM,306
../../../bin/mlx_lm.chat,sha256=uJ237uVoO37I7682LRslYSoBAXp5Uw_pOlho5wLPB_c,298
../../../bin/mlx_lm.convert,sha256=AgJqzJrJJtGMAe9JXTD7b-wbSUsM0zkC4U0OyvIWZkE,301
../../../bin/mlx_lm.dwq,sha256=0cWfOvMraPnizI9EUhzKi2EC4uVSw73LTtkP_Wp8vF4,303
../../../bin/mlx_lm.dynamic_quant,sha256=B54OpMZTC6PD6Nk8huaSzZSqgVB1L-cqKZL2XqKCI6g,313
../../../bin/mlx_lm.evaluate,sha256=dwla0Z5tbH51mF8UHUnHDie43Kr945MWNri0LfFUQW4,302
../../../bin/mlx_lm.fuse,sha256=X3rI7W0WD7nyOBaJZ19cTKIVbSCBdxD__7LmtNSA4Cg,298
../../../bin/mlx_lm.generate,sha256=hTykJTzjmzp2MFlXwmf8sSDUPoRN0ffo5t_3d2C6w8k,302
../../../bin/mlx_lm.lora,sha256=N3NQZ6x56lGOIn5zEF9-Oj8hlEVWIo-RiVW45Vv226k,298
../../../bin/mlx_lm.manage,sha256=RNv-j70hJvcUwkQdg62PclXxCGcwwWDs1NxhLijoT4U,300
../../../bin/mlx_lm.server,sha256=GKD-oDHfsHFpoQdZ0lmrJeV_yFOSFAZreE1VTwPtAVo,300
../../../bin/mlx_lm.upload,sha256=bZUPy-ftmNj-MkNtUwUWDOJCJ0yNE65igmTrTfr55x0,300
mlx_lm-0.25.3.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
mlx_lm-0.25.3.dist-info/METADATA,sha256=8h2umwlBZeGLYY6DM0fjRcDR1jW9M6LJWyqOgT3TMUM,10189
mlx_lm-0.25.3.dist-info/RECORD,,
mlx_lm-0.25.3.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mlx_lm-0.25.3.dist-info/WHEEL,sha256=_zCd3N1l69ArxyTb8rzEoP9TpbYXkqRFSNOD5OuxnTs,91
mlx_lm-0.25.3.dist-info/entry_points.txt,sha256=6QD3kvfqag6dn_90kcMWROZvLARslxIKc0ITw_oOpK0,503
mlx_lm-0.25.3.dist-info/licenses/LICENSE,sha256=zPq3zLLqMG9xUxyMp3u1VQdgbNkHaLHjK4tSq1tIzwE,1066
mlx_lm-0.25.3.dist-info/top_level.txt,sha256=dQA-y6yw3xk0oKa3nHVbKvJBfy-PCsOT0Vzm3BzywCY,7
mlx_lm/__init__.py,sha256=v8KkXKL2nd5OFU4QSaRPE_KPWCUBtTrrV3fuOur_d6U,239
mlx_lm/__main__.py,sha256=jHR60c1pOVv-tm5Rxml4BbCZOUYnu3MXTATRYMAs-pc,698
mlx_lm/__pycache__/__init__.cpython-311.pyc,,
mlx_lm/__pycache__/__main__.cpython-311.pyc,,
mlx_lm/__pycache__/_version.cpython-311.pyc,,
mlx_lm/__pycache__/cache_prompt.cpython-311.pyc,,
mlx_lm/__pycache__/chat.cpython-311.pyc,,
mlx_lm/__pycache__/convert.cpython-311.pyc,,
mlx_lm/__pycache__/evaluate.cpython-311.pyc,,
mlx_lm/__pycache__/fuse.cpython-311.pyc,,
mlx_lm/__pycache__/generate.cpython-311.pyc,,
mlx_lm/__pycache__/gguf.cpython-311.pyc,,
mlx_lm/__pycache__/lora.cpython-311.pyc,,
mlx_lm/__pycache__/manage.cpython-311.pyc,,
mlx_lm/__pycache__/sample_utils.cpython-311.pyc,,
mlx_lm/__pycache__/server.cpython-311.pyc,,
mlx_lm/__pycache__/tokenizer_utils.cpython-311.pyc,,
mlx_lm/__pycache__/upload.cpython-311.pyc,,
mlx_lm/__pycache__/utils.cpython-311.pyc,,
mlx_lm/_version.py,sha256=ICY_i9W0aOh6Vg-QgSKdHrifURumDHfo34DpOC8pZL0,60
mlx_lm/cache_prompt.py,sha256=auNEpbR6BT8zTGtzM307V04TEyLoqIQZnGrcayhCPgQ,4768
mlx_lm/chat.py,sha256=RYMhVw93Qi18saWQUvvUP-YSmZ3yx_CEnxQzPu3rPaI,3779
mlx_lm/convert.py,sha256=4cujTVrfy7JRVyXLnc72dAQtVXSW2ZQcA_oaVq9HDtw,7034
mlx_lm/evaluate.py,sha256=R3_TX9NKXHZPfgmSX6x7n0ng8ZZm-uKnvATyGqn9x6g,15939
mlx_lm/fuse.py,sha256=MRebAalJAi4sG5rT_9YL2iyW4MgZj51tK-4PkhiS39w,3518
mlx_lm/generate.py,sha256=IZi8lVqg2PUJaKlByC-iZo4cON7tpXvJej8VpN0U8dM,30807
mlx_lm/gguf.py,sha256=VrNbb1lC_xhM6edWyUyw5qHYXglPX1LtYjLYxIyyJHs,11475
mlx_lm/lora.py,sha256=Dl2ml1QvV4oWX6a7JBEMoU9nRPVsqsx3dZCs5uEt15I,9991
mlx_lm/manage.py,sha256=_PdPyhte4SgnwRBKKN_fESBOFnKrL9wQ7XzOUaP9vtU,4706
mlx_lm/models/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mlx_lm/models/__pycache__/__init__.cpython-311.pyc,,
mlx_lm/models/__pycache__/afm7.cpython-311.pyc,,
mlx_lm/models/__pycache__/baichuan_m1.cpython-311.pyc,,
mlx_lm/models/__pycache__/base.cpython-311.pyc,,
mlx_lm/models/__pycache__/cache.cpython-311.pyc,,
mlx_lm/models/__pycache__/cohere.cpython-311.pyc,,
mlx_lm/models/__pycache__/cohere2.cpython-311.pyc,,
mlx_lm/models/__pycache__/dbrx.cpython-311.pyc,,
mlx_lm/models/__pycache__/deepseek.cpython-311.pyc,,
mlx_lm/models/__pycache__/deepseek_v2.cpython-311.pyc,,
mlx_lm/models/__pycache__/deepseek_v3.cpython-311.pyc,,
mlx_lm/models/__pycache__/exaone.cpython-311.pyc,,
mlx_lm/models/__pycache__/gemma.cpython-311.pyc,,
mlx_lm/models/__pycache__/gemma2.cpython-311.pyc,,
mlx_lm/models/__pycache__/gemma3.cpython-311.pyc,,
mlx_lm/models/__pycache__/gemma3_text.cpython-311.pyc,,
mlx_lm/models/__pycache__/gemma3n.cpython-311.pyc,,
mlx_lm/models/__pycache__/glm4.cpython-311.pyc,,
mlx_lm/models/__pycache__/gpt2.cpython-311.pyc,,
mlx_lm/models/__pycache__/gpt_bigcode.cpython-311.pyc,,
mlx_lm/models/__pycache__/gpt_neox.cpython-311.pyc,,
mlx_lm/models/__pycache__/granite.cpython-311.pyc,,
mlx_lm/models/__pycache__/helium.cpython-311.pyc,,
mlx_lm/models/__pycache__/hunyuan.cpython-311.pyc,,
mlx_lm/models/__pycache__/internlm2.cpython-311.pyc,,
mlx_lm/models/__pycache__/internlm3.cpython-311.pyc,,
mlx_lm/models/__pycache__/kimi_vl.cpython-311.pyc,,
mlx_lm/models/__pycache__/llama.cpython-311.pyc,,
mlx_lm/models/__pycache__/llama4.cpython-311.pyc,,
mlx_lm/models/__pycache__/mamba.cpython-311.pyc,,
mlx_lm/models/__pycache__/mimo.cpython-311.pyc,,
mlx_lm/models/__pycache__/minicpm.cpython-311.pyc,,
mlx_lm/models/__pycache__/minicpm3.cpython-311.pyc,,
mlx_lm/models/__pycache__/mistral3.cpython-311.pyc,,
mlx_lm/models/__pycache__/mixtral.cpython-311.pyc,,
mlx_lm/models/__pycache__/nemotron-nas.cpython-311.pyc,,
mlx_lm/models/__pycache__/nemotron.cpython-311.pyc,,
mlx_lm/models/__pycache__/olmo.cpython-311.pyc,,
mlx_lm/models/__pycache__/olmo2.cpython-311.pyc,,
mlx_lm/models/__pycache__/olmoe.cpython-311.pyc,,
mlx_lm/models/__pycache__/openelm.cpython-311.pyc,,
mlx_lm/models/__pycache__/phi.cpython-311.pyc,,
mlx_lm/models/__pycache__/phi3.cpython-311.pyc,,
mlx_lm/models/__pycache__/phi3small.cpython-311.pyc,,
mlx_lm/models/__pycache__/phimoe.cpython-311.pyc,,
mlx_lm/models/__pycache__/phixtral.cpython-311.pyc,,
mlx_lm/models/__pycache__/pixtral.cpython-311.pyc,,
mlx_lm/models/__pycache__/plamo.cpython-311.pyc,,
mlx_lm/models/__pycache__/plamo2.cpython-311.pyc,,
mlx_lm/models/__pycache__/qwen.cpython-311.pyc,,
mlx_lm/models/__pycache__/qwen2.cpython-311.pyc,,
mlx_lm/models/__pycache__/qwen2_moe.cpython-311.pyc,,
mlx_lm/models/__pycache__/qwen3.cpython-311.pyc,,
mlx_lm/models/__pycache__/qwen3_moe.cpython-311.pyc,,
mlx_lm/models/__pycache__/recurrent_gemma.cpython-311.pyc,,
mlx_lm/models/__pycache__/rope_utils.cpython-311.pyc,,
mlx_lm/models/__pycache__/stablelm.cpython-311.pyc,,
mlx_lm/models/__pycache__/starcoder2.cpython-311.pyc,,
mlx_lm/models/__pycache__/switch_layers.cpython-311.pyc,,
mlx_lm/models/afm7.py,sha256=o1lPTuZ2w88EtH_FbONXrFgejCN4wXrSYYcOLofSg3Y,12296
mlx_lm/models/baichuan_m1.py,sha256=gRNrYqayNiHjnsYorTP9WeaSr5NHg4LyuUz24cWPvFE,7874
mlx_lm/models/base.py,sha256=5IPVU9hF25toH7gSFcVusIWStnOH-jXy1cAtDOC3zCo,3673
mlx_lm/models/cache.py,sha256=KIW_WLP6iSBI-EECxEb1PkWl0mVz-Hq6D4l4JbP8tLc,17869
mlx_lm/models/cohere.py,sha256=1b1XGmP0_LINUlJLFctSOcemsusvbcpQsRLXcYBrc1o,5925
mlx_lm/models/cohere2.py,sha256=EbZVhcNQL8GeK1x1-JPbd0x9igkolR15MjNKvMzOGgg,7346
mlx_lm/models/dbrx.py,sha256=jn3I2WGh4tw67Z-BFSNKrrd-R5pdy2Ys31wwppiKOhA,8036
mlx_lm/models/deepseek.py,sha256=JHrQVihH-yV2zM9l22Vutfif4YWJuo0vkF6OZ64k9r0,8803
mlx_lm/models/deepseek_v2.py,sha256=zTKmZ_uBJlhXFEnEE7dTVUSzA4uALA-6-r1KPf4aH_g,15884
mlx_lm/models/deepseek_v3.py,sha256=00TBYU6C05ew3rnbATeB6TkqvZyHKV5JKCjXZfAldBQ,18551
mlx_lm/models/exaone.py,sha256=i8DSJwa88v5Ml7hOrEUdkXGCjitWeshnGgja7-o8e4U,5297
mlx_lm/models/gemma.py,sha256=huYkwOXdHAbdo_rqw7OK36ZMiBUtVDvwVvMAJkR-taA,5412
mlx_lm/models/gemma2.py,sha256=x9lkmmMQ7ZK1CR2bbaXmraRdABflEcnypY2gnugakXE,6813
mlx_lm/models/gemma3.py,sha256=TMMD54nhKftA8h8usvV33loZBO1c__h3tULeUXuhSvk,1860
mlx_lm/models/gemma3_text.py,sha256=gRbCUYB55IqNmJOBhIb7fCN56Pre21fyWRPJMd2_YPo,8402
mlx_lm/models/gemma3n.py,sha256=OUGFFojtEB-kgbNA5VAY7Rs1odBA-aLgO7zAKibvue0,21607
mlx_lm/models/glm4.py,sha256=bP41lByBWMJ7jMreQKjhgnowAX1ufpE0TsK6cfl1ipM,5683
mlx_lm/models/gpt2.py,sha256=fEc1Cg-JCcQwoSNMlDI0GvBdxXn-YK8Qr6PtsFU3qsg,6174
mlx_lm/models/gpt_bigcode.py,sha256=sqHema_bujCkMvvzMr4SNzoMiYHAu4YdCu72IpFBPl0,5568
mlx_lm/models/gpt_neox.py,sha256=lwaxoV3Vjtzb1GgsUNui0alDn0J7x6xs8rLi2cDdyok,6580
mlx_lm/models/granite.py,sha256=DZAl1_yj6nE4wwXSO3rTm5_v3lvq3oz7jP3hbFzdUjM,6235
mlx_lm/models/helium.py,sha256=mhGU-uOc0rdlgF_0bpwsoID6CNAGYSAusPYsQ2W_P5M,5727
mlx_lm/models/hunyuan.py,sha256=5dpZlM8qpkh3PHBXHO7x3aykIEgeZWxJFR2SErpW-Bk,10628
mlx_lm/models/internlm2.py,sha256=2ziXNbVIqo2m2PgIN-8dwHdyOBZon1Q9-5xsuA1Ystg,7753
mlx_lm/models/internlm3.py,sha256=wdpe5qHPB81CrFWPzS4YkRybgiKCGkLBOaPx4H9zDg4,7813
mlx_lm/models/kimi_vl.py,sha256=uPLwW-LPT60tw2H-PgI0gDyiMgklMWr_rETlbx6Az90,3535
mlx_lm/models/llama.py,sha256=SUPF00JeApmIuhXlMhbBDWehvhIcBxlu_dO3_SuL514,6777
mlx_lm/models/llama4.py,sha256=zlNJ7lAlAOdvgs7BEPRC5DrBk3mV2dwa0EmmE9rchp8,11009
mlx_lm/models/mamba.py,sha256=HQqpBtx8485UramaJizShcSw7Dn_RVNdXYHseAJFc50,7865
mlx_lm/models/mimo.py,sha256=nNkH4BnOJV2Pm4jnZwL843FlC4MJq7AG3vYJeLAtGik,6068
mlx_lm/models/minicpm.py,sha256=w5lvsR9ZBHpimTBBHJN1M3xIWxJc12_B4VLNMzR9HfM,6427
mlx_lm/models/minicpm3.py,sha256=HOZA-JQzZDVHrc9ZAD2Fm92JlSMVYq7RDgL0HcGLBdY,8136
mlx_lm/models/mistral3.py,sha256=yMrsyLRQApUmVQifuzxO4CZUGz3AEwWdFLO8pzPZhZ0,1277
mlx_lm/models/mixtral.py,sha256=-w3cp4jWrCH-8wwSufY0xHExv5H8d5eXmxaEh5PZnyI,7157
mlx_lm/models/nemotron-nas.py,sha256=3T-4MiBDq-hi9AjnwIX3RxuI0i_1VpW3IZyByPPhV1o,13791
mlx_lm/models/nemotron.py,sha256=8sMZAd4F24HQI9TP-41L0z7VGRPmvWgkd7qOUG3i3WU,7137
mlx_lm/models/olmo.py,sha256=P1oo3Qr3eWOv99mvW2IGTntOLTb_oMEJFZeaB2u_AcY,4966
mlx_lm/models/olmo2.py,sha256=nBPDBQI9MCxIPtCPqy3oA8VTUfk7IT4k6iSNoPpB2Yk,6685
mlx_lm/models/olmoe.py,sha256=ZC6K4-OQuTPoVd8ixHT8OEhlUd-8ddE62XJqvmVv3JY,7505
mlx_lm/models/openelm.py,sha256=vuTUtyAuMnB-wSadzpjiQ4Njpj38SwrkcBHSzYsbo2Q,6862
mlx_lm/models/phi.py,sha256=yZ6utUcjltxUDxgsUIwRP4D8TWeNwKWDd_s8ODJWNB8,5635
mlx_lm/models/phi3.py,sha256=v0t9V5a3IgCZHpFM5A4nNu6VKAcXs-vRoC_80QyLb1E,7349
mlx_lm/models/phi3small.py,sha256=n2BXqbA8Y0fiES8xuDi0k7QK_so40y2W9DqACxkKc2U,10280
mlx_lm/models/phimoe.py,sha256=B6yxbiSlq_QUr6Dwx2O5Ugn1sekg5sIVc1KBMhXfdJE,7271
mlx_lm/models/phixtral.py,sha256=iYfNFxbn7TLqemF9utenqC1-5j7YZc_w5FlIhIVT3S4,6127
mlx_lm/models/pixtral.py,sha256=FAYN6WpmyKC21lToCZLK6smfZg14giqmY0RHa3oHL5o,1397
mlx_lm/models/plamo.py,sha256=aOUggqXNJtnX-qtNTbjvYXda0m_eucg4BWUvqHhv-t8,6620
mlx_lm/models/plamo2.py,sha256=Los3OWZ0RZsa6uBIvnOafgPKoqAPjjxAlnzBRz3YOoo,18843
mlx_lm/models/qwen.py,sha256=NO0qSW7VAydNhxAMiKtpjazd5oQh3zFm8mDkXv0PQEc,4777
mlx_lm/models/qwen2.py,sha256=3Njum8FdtZuIx5MneEieUflX7B-JXRhv8emWj5n20RM,6206
mlx_lm/models/qwen2_moe.py,sha256=nIWNaQnGq0UQBJ_v3epcnji-QfPM2EsXcPd_TVs9GCw,7950
mlx_lm/models/qwen3.py,sha256=_mMv5N9zNdHzR9UHp2UijwOx6Dja5-tS0iWJB8lz9qE,5904
mlx_lm/models/qwen3_moe.py,sha256=3i0DZqbNL8670xRZqc-l0rrTbNfXLLeXCEsS95h0rEM,7683
mlx_lm/models/recurrent_gemma.py,sha256=GPTMSgvCKC0kn1_Rv5UzFcetTSxAsvtxDabLqpB9rtM,13209
mlx_lm/models/rope_utils.py,sha256=ty0dA3SsEUFtFbHo16tKdnKymrNKKsUO3KMYapMajbY,8704
mlx_lm/models/stablelm.py,sha256=JM7mKbEERWHwSbvOeEIm1QI3uRou8w9ZFlZucfHK8qQ,6839
mlx_lm/models/starcoder2.py,sha256=NR3AI2lV9-jWNp21Z5k7EKsyTQBI_SHUIscuiTiK9Ws,5223
mlx_lm/models/switch_layers.py,sha256=WN_ljZRZkPgkPsdaUa1FUrVM-NTP3pm_H_cgReb2U48,6013
mlx_lm/py.typed,sha256=AbpHGcgLb-kRsJGnwFEktk7uzpZOCcBY74-YBdrKVGs,1
mlx_lm/quant/__pycache__/awq.cpython-311.pyc,,
mlx_lm/quant/__pycache__/dwq.cpython-311.pyc,,
mlx_lm/quant/__pycache__/dynamic_quant.cpython-311.pyc,,
mlx_lm/quant/__pycache__/utils.cpython-311.pyc,,
mlx_lm/quant/awq.py,sha256=cbY3-JHdUNJ5XZn1UPxHACoc5yIQhJwKUUimaLgSvfg,17903
mlx_lm/quant/dwq.py,sha256=pY8N2gqqDznUHIZCNzSlMto0iu35NtHjkAXAe46WRcw,7673
mlx_lm/quant/dynamic_quant.py,sha256=zrKS8durrJjSMriRFSVrP_3vZ4iqL1_h3dMZFMy9xiE,7434
mlx_lm/quant/utils.py,sha256=-65Up-ObmumZvt_r-DPoZeaRK62in0w_5TODudhlXlg,998
mlx_lm/sample_utils.py,sha256=KyAbIcFUQy6bMM3G28khJ4ulSph0xUxBijLQPfHXH5g,10758
mlx_lm/server.py,sha256=B_4mjfXCWq2blMmIXiJ0zAwa_upj4s8nv6qzVQ2Up3k,38161
mlx_lm/tokenizer_utils.py,sha256=D8R8jAc8ClkiH6D5IJ0XWkkCBXAdLpXAH90qVk35o2o,14885
mlx_lm/tuner/__init__.py,sha256=oDxjfHlSESoJkGtud8r12t86zuscJxb9ELAa_TGGc6M,92
mlx_lm/tuner/__pycache__/__init__.cpython-311.pyc,,
mlx_lm/tuner/__pycache__/callbacks.cpython-311.pyc,,
mlx_lm/tuner/__pycache__/datasets.cpython-311.pyc,,
mlx_lm/tuner/__pycache__/dora.cpython-311.pyc,,
mlx_lm/tuner/__pycache__/lora.cpython-311.pyc,,
mlx_lm/tuner/__pycache__/trainer.cpython-311.pyc,,
mlx_lm/tuner/__pycache__/utils.cpython-311.pyc,,
mlx_lm/tuner/callbacks.py,sha256=8Pc8raq6Ng-LcuK9JHSAjDzFbhqpMPF9AhwoRHsITVY,1307
mlx_lm/tuner/datasets.py,sha256=nxfhn03HRxw9u7mgRmvs36jGBHvRRQ9jU38OtmfL8Os,9104
mlx_lm/tuner/dora.py,sha256=jBpjvaGGkk79-eWenufLOfKRqu24y8ebPEJM85mF5bA,6894
mlx_lm/tuner/lora.py,sha256=oAvqfS4whisypyQjoxrUbjazXLvwbCWvt1Njqj-eAlc,8357
mlx_lm/tuner/trainer.py,sha256=Ywx3DubwDHOoNw_MAZ9BiSaniijjGlwF1fnlXywWgJQ,11137
mlx_lm/tuner/utils.py,sha256=rFE6SqIGK1lotsanaBcSHbzl_N_388UYaxnhh9bmBRo,9520
mlx_lm/upload.py,sha256=0lxUP1TFi9z3Vc7uqdndo2FppwTidU6hB1-sEZA46z0,526
mlx_lm/utils.py,sha256=SO37Y5gqgAj01X3w0-O1fZ8RoUHBF224BYqAPBuWXY0,17241
