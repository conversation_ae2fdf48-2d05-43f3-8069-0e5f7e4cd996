# Interactive Text-to-Speech CLI

A Python interactive CLI that uses the CSM (Conversational Speech Model) from Se<PERSON> to convert text to speech, optimized for Apple Silicon Macs using MLX.

## Features

- 🎙️ **Interactive CLI**: Type text and hear it spoken immediately
- 🚀 **Apple Silicon Optimized**: Uses MLX framework for fast inference on M-series chips
- 🧠 **Advanced AI Model**: Powered by Sesame's CSM-1B model
- 🎵 **High Quality Audio**: 24kHz audio output with natural speech
- ⚡ **Fast Performance**: Optimized for real-time speech generation with quantization
- 🔄 **Continuous Mode**: Keep typing and speaking without restarting
- 🎤 **Voice Cloning**: Uses reference audio for personalized voice generation
- ⏱️ **Latency Monitoring**: Real-time performance metrics and timing reports
- ⚙️ **Performance Tuning**: Adjustable settings for speed vs quality optimization

## Requirements

- **macOS** with Apple Silicon (M1, M2, M3, M4) for optimal performance
- **Python 3.8-3.12** (Python 3.13+ not supported due to dependency issues)
- **Hugging Face Account** (API key provided)

## Quick Start

1. **Clone or download** this project to your Mac

2. **Run the setup script**:
   ```bash
   python setup.py
   ```

3. **Start the interactive CLI**:
   ```bash
   python tts_cli.py
   ```

4. **Start speaking**! Type any text and press Enter to hear it spoken.

## Manual Installation

If the setup script doesn't work, install dependencies manually:

```bash
# Install dependencies
pip install -r requirements.txt

# Run the CLI
python tts_cli.py
```

## Usage

Once the CLI is running:

- **Type any text** and press Enter to hear it spoken
- **Special commands**:
  - `quit` or `exit` - Close the program
  - `help` - Show help information
  - `settings` - View/change performance settings
  - `status` - Show current engine status
- **Performance commands**:
  - `quantize` - Enable quantization for faster inference
  - `temp X` - Set temperature (0.1-2.0, default 0.8)
  - `topk X` - Set top-k sampling (1-100, default 50)
  - `topp X` - Set top-p sampling (0.1-1.0)
  - `minp X` - Set min-p sampling (0.01-0.5, default 0.05)
  - `fast` - Apply fast preset (quantized + optimized settings)
  - `quality` - Apply quality preset (high-quality settings)
  - `balanced` - Apply balanced preset (default settings)
- **Interrupt playback** with Ctrl+C
- **Exit program** with Ctrl+C (twice)

### Example Session

```
🎙️  Interactive Text-to-Speech CLI
   Powered by CSM model on Apple Silicon MLX
============================================================

📝 Instructions:
  • Type any text and press Enter to hear it spoken
  • Type 'quit' or 'exit' to close the program
  • Type 'help' to see this message again
  • Use Ctrl+C to interrupt audio playback

🎯 Ready to speak! What would you like me to say?

🚀 Ready! Type your text below:

💬 You: Hello, this is a test of the text to speech system.
🔄 Processing...
🎤 Generating speech for: 'Hello, this is a test of the text to speech system.'
✅ Done!

💬 You: quit
👋 Goodbye!
```

## How It Works

1. **Model Loading**: Downloads and caches the CSM-1B model from Hugging Face
2. **MLX Optimization**: Uses Apple's MLX framework for efficient inference on Apple Silicon
3. **Speech Generation**: Converts text to high-quality speech using the CSM model
4. **Audio Playback**: Plays generated audio immediately using pygame

## Performance

On Apple Silicon Macs:
- **First run**: ~30-60 seconds (model download and loading)
- **Subsequent runs**: ~5-10 seconds (model loading only)
- **Speech generation**: ~2-5 seconds per sentence
- **Audio quality**: 24kHz, natural-sounding speech

## Troubleshooting

### Common Issues

1. **Python version error**:
   - Use Python 3.8-3.12 (not 3.13+)
   - Check with: `python --version`

2. **Installation fails**:
   - Try: `pip install --upgrade pip`
   - Then: `pip install -r requirements.txt`

3. **Model download fails**:
   - Check internet connection
   - Verify Hugging Face API key is valid

4. **Audio doesn't play**:
   - Check system audio settings
   - Try restarting the program

5. **Slow performance**:
   - Ensure you're on Apple Silicon Mac
   - Close other resource-intensive applications

### Getting Help

If you encounter issues:
1. Check the error messages in the terminal
2. Verify all requirements are met
3. Try restarting the program
4. Check system resources (memory, disk space)

## Technical Details

- **Model**: Sesame CSM-1B (Conversational Speech Model)
- **Framework**: MLX (Apple's machine learning framework)
- **Audio**: 24kHz WAV output via pygame
- **Platform**: Optimized for Apple Silicon, compatible with Intel Macs
- **Dependencies**: csm-mlx, huggingface_hub, pygame, numpy, audiofile

## License

This project uses the CSM model from Sesame under the Apache 2.0 license.

## Acknowledgments

- **Sesame** for the CSM model
- **senstella** for the MLX implementation (csm-mlx)
- **Apple** for the MLX framework
- **Hugging Face** for model hosting and distribution
