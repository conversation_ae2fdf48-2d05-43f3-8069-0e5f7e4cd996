#!/usr/bin/env python3
"""
Interactive Text-to-Speech CLI using CSM model optimized for Apple Silicon
"""

import sys
import tempfile
import time
from pathlib import Path
from typing import Optional

import numpy as np
import pygame
from huggingface_hub import hf_hub_download, login
from mlx_lm.sample_utils import make_sampler
import mlx.core as mx
from mlx import nn
import audresample

try:
    from csm_mlx import CSM, csm_1b, generate, Segment
    import audiofile
except ImportError as e:
    print(f"Error importing csm_mlx: {e}")
    print("Please install the required dependencies:")
    print("pip install -r requirements.txt")
    sys.exit(1)


class TTSEngine:
    def __init__(self, hf_token: str, reference_audio_path: Optional[str] = None):
        """Initialize the TTS engine with Hugging Face authentication."""
        self.hf_token = hf_token
        self.model = None
        self.temp_dir = Path(tempfile.gettempdir()) / "tts_audio"
        self.temp_dir.mkdir(exist_ok=True)

        # Performance settings
        self.quantized = False
        self.temperature = 0.8
        self.top_k = 50
        self.top_p = None
        self.min_p = 0.05

        # Reference audio for voice cloning
        self.reference_audio_path = reference_audio_path
        self.reference_context = []

        # Conversation memory for natural speech
        self.conversation_history = []
        self.max_conversation_length = 8  # Keep last 8 exchanges
        self.conversation_mode = True

        # Latency tracking
        self.last_generation_time = 0.0
        self.last_model_load_time = 0.0

        # Initialize pygame mixer for audio playback
        pygame.mixer.init(frequency=24000, size=-16, channels=1, buffer=512)
        
    def read_audio(self, audio_path: str, sampling_rate: int = 24000) -> Optional[mx.array]:
        """Read and resample audio file for use with CSM model."""
        try:
            signal, original_sampling_rate = audiofile.read(audio_path, always_2d=True)
            signal = audresample.resample(signal, original_sampling_rate, sampling_rate)
            signal = mx.array(signal)

            if signal.shape[0] >= 1:
                signal = signal.mean(axis=0)
            else:
                signal = signal.squeeze(0)

            return signal
        except Exception as e:
            print(f"❌ Error reading audio file {audio_path}: {e}")
            return None

    def setup_reference_audio(self):
        """Setup reference audio for voice cloning."""
        if not self.reference_audio_path:
            return

        try:
            print(f"🎤 Loading reference audio: {self.reference_audio_path}")
            audio_data = self.read_audio(self.reference_audio_path)

            if audio_data is not None:
                # Create a reference segment for voice cloning
                self.reference_context = [
                    Segment(
                        speaker=0,
                        text="Reference audio for voice cloning",
                        audio=audio_data
                    )
                ]
                print("✅ Reference audio loaded successfully!")
            else:
                print("❌ Failed to load reference audio")

        except Exception as e:
            print(f"❌ Error setting up reference audio: {e}")

    def setup_model(self) -> bool:
        """Download and load the CSM model."""
        try:
            start_time = time.time()

            print("🔐 Authenticating with Hugging Face...")
            login(token=self.hf_token)

            print("📥 Downloading CSM model (this may take a while on first run)...")
            weight_path = hf_hub_download(
                repo_id="senstella/csm-1b-mlx",
                filename="ckpt.safetensors"
            )

            print("🧠 Loading model...")
            self.model = CSM(csm_1b())
            self.model.load_weights(weight_path)

            # Setup reference audio if provided
            self.setup_reference_audio()

            self.last_model_load_time = time.time() - start_time
            print(f"✅ Model loaded successfully! (Load time: {self.last_model_load_time:.2f}s)")
            return True

        except Exception as e:
            print(f"❌ Error setting up model: {e}")
            return False
    
    def apply_quantization(self):
        """Apply quantization to speed up inference."""
        if self.model and not self.quantized:
            print("⚡ Applying quantization for faster inference...")
            nn.quantize(self.model)
            self.quantized = True
            print("✅ Quantization applied!")

    def update_settings(self, **kwargs):
        """Update generation settings."""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
                print(f"🔧 Updated {key} = {value}")

    def add_to_conversation(self, user_text: str, generated_audio: Optional[np.ndarray] = None):
        """Add an exchange to conversation history for better context."""
        if not self.conversation_mode:
            return

        # Create conversation segment
        segment = Segment(
            speaker=0,  # User is speaker 0, AI is speaker 1
            text=user_text,
            audio=mx.array(generated_audio) if generated_audio is not None else None
        )

        self.conversation_history.append(segment)

        # Keep only recent conversation
        if len(self.conversation_history) > self.max_conversation_length:
            self.conversation_history = self.conversation_history[-self.max_conversation_length:]

    def get_conversation_context(self):
        """Get current conversation context for generation."""
        if not self.conversation_mode:
            return self.reference_context if self.reference_context else []

        # Combine reference context with conversation history
        context = []
        if self.reference_context:
            context.extend(self.reference_context)
        context.extend(self.conversation_history)
        return context

    def clear_conversation(self):
        """Clear conversation history."""
        self.conversation_history = []
        print("🗑️  Conversation history cleared")

    def get_conversation_summary(self):
        """Get summary of current conversation."""
        if not self.conversation_history:
            return "No conversation history"

        summary = []
        for i, segment in enumerate(self.conversation_history):
            speaker = "You" if segment.speaker == 0 else "AI"
            text_preview = segment.text[:50] + "..." if len(segment.text) > 50 else segment.text
            summary.append(f"  {i+1}. {speaker}: {text_preview}")

        return "\n".join(summary)

    def generate_speech(self, text: str, speaker: int = 0) -> Optional[np.ndarray]:
        """Generate speech from text."""
        if not self.model:
            print("❌ Model not loaded!")
            return None

        try:
            start_time = time.time()

            print(f"🎤 Generating speech for: '{text[:50]}{'...' if len(text) > 50 else ''}'")

            # Prepare sampler with current settings
            sampler_kwargs = {'temp': self.temperature}
            if self.top_k is not None:
                sampler_kwargs['top_k'] = int(self.top_k)
            if self.top_p is not None:
                sampler_kwargs['top_p'] = self.top_p
            if self.min_p is not None:
                sampler_kwargs['min_p'] = self.min_p

            sampler = make_sampler(**sampler_kwargs)

            # Use conversation context for more natural speech
            context = self.get_conversation_context()

            # Generate audio using the CSM model
            audio = generate(
                self.model,
                text=text,
                speaker=speaker,
                context=context,
                max_audio_length_ms=10_000,
                sampler=sampler
            )

            self.last_generation_time = time.time() - start_time

            return np.asarray(audio)

        except Exception as e:
            print(f"❌ Error generating speech: {e}")
            return None
    
    def play_audio(self, audio_data: np.ndarray) -> bool:
        """Play audio data using pygame."""
        try:
            # Save audio to temporary file
            temp_file = self.temp_dir / f"temp_audio_{int(time.time())}.wav"
            audiofile.write(str(temp_file), audio_data, 24000)
            
            # Load and play the audio
            pygame.mixer.music.load(str(temp_file))
            pygame.mixer.music.play()
            
            # Wait for playback to finish
            while pygame.mixer.music.get_busy():
                pygame.time.wait(100)
            
            # Clean up temporary file
            temp_file.unlink(missing_ok=True)
            
            return True
            
        except Exception as e:
            print(f"❌ Error playing audio: {e}")
            return False
    
    def speak(self, text: str, speaker: int = 0) -> bool:
        """Generate and play speech from text."""
        total_start_time = time.time()

        audio_data = self.generate_speech(text, speaker)
        if audio_data is not None:
            # Add to conversation history for better context
            self.add_to_conversation(text, audio_data)

            playback_success = self.play_audio(audio_data)

            total_time = time.time() - total_start_time

            # Report latency
            print(f"⏱️  Latency Report:")
            print(f"   Generation: {self.last_generation_time:.2f}s")
            print(f"   Total: {total_time:.2f}s")
            if self.quantized:
                print(f"   (Quantized mode)")
            if self.conversation_mode and len(self.conversation_history) > 1:
                print(f"   Context: {len(self.conversation_history)} conversation turns")

            return playback_success
        return False

    def get_status(self) -> str:
        """Get current engine status."""
        status = []
        status.append(f"Model: {'Loaded' if self.model else 'Not loaded'}")
        status.append(f"Quantized: {'Yes' if self.quantized else 'No'}")
        status.append(f"Reference Audio: {'Yes' if self.reference_context else 'No'}")
        status.append(f"Conversation Mode: {'Yes' if self.conversation_mode else 'No'}")
        status.append(f"Conversation History: {len(self.conversation_history)} turns")
        status.append(f"Temperature: {self.temperature}")
        status.append(f"Top-K: {self.top_k}")
        status.append(f"Top-P: {self.top_p}")
        status.append(f"Min-P: {self.min_p}")
        if self.last_model_load_time > 0:
            status.append(f"Last Load Time: {self.last_model_load_time:.2f}s")
        if self.last_generation_time > 0:
            status.append(f"Last Generation Time: {self.last_generation_time:.2f}s")
        return "\n".join([f"   {s}" for s in status])


def print_welcome():
    """Print welcome message and instructions."""
    print("\n" + "="*70)
    print("🎙️  Interactive Text-to-Speech CLI")
    print("   Powered by CSM model on Apple Silicon MLX")
    print("   🎤 With Voice Cloning & Conversational Context")
    print("="*70)
    print("\n📝 Instructions:")
    print("  • Type any text and press Enter to hear it spoken")
    print("  • Type 'quit' or 'exit' to close the program")
    print("  • Type 'help' to see this message again")
    print("  • Type 'natural' for tips on human-like speech")
    print("  • Type 'settings' to view/change performance settings")
    print("  • Type 'status' to see current engine status")
    print("  • Use Ctrl+C to interrupt audio playback")
    print("\n💡 Pro Tip: The more you chat, the more natural it sounds!")
    print("   Try: 'Hey there! How's it going?' instead of 'Hello.'")
    print("\n🎯 Ready to speak! What would you like me to say?\n")


def print_help():
    """Print help information."""
    print("\n📚 Help:")
    print("  • Just type any text and press Enter")
    print("  • The AI will generate speech and play it automatically")
    print("  • Special commands:")
    print("    - 'quit' or 'exit': Close the program")
    print("    - 'help': Show this help message")
    print("    - 'settings': View/change performance settings")
    print("    - 'status': Show current engine status")
    print("    - 'natural': Tips for more human/conversational speech")
    print("  • Conversation commands:")
    print("    - 'conversation': View conversation history")
    print("    - 'clear': Clear conversation history")
    print("    - 'examples': Show natural speech examples")
    print("  • Performance commands:")
    print("    - 'quantize': Enable quantization for faster inference")
    print("    - 'temp X': Set temperature (0.1-2.0, default 0.8)")
    print("    - 'topk X': Set top-k sampling (1-100, default 50)")
    print("    - 'topp X': Set top-p sampling (0.1-1.0)")
    print("    - 'minp X': Set min-p sampling (0.01-0.5, default 0.05)")
    print("  • Tips for natural speech:")
    print("    - Use conversational language: 'Hey!', 'Well...', 'You know'")
    print("    - Add emotions: 'I'm excited!', 'That's amazing!'")
    print("    - Use contractions: 'I'm', 'don't', 'can't'")
    print("    - The more you chat, the more natural it gets!")
    print("    - Press Ctrl+C during playback to stop audio")
    print()

def print_settings_help():
    """Print settings help information."""
    print("\n⚙️  Performance Settings:")
    print("  • quantize - Enable quantization for ~2x speed boost")
    print("  • temp X - Sampling temperature (0.1-2.0)")
    print("    - Lower = more consistent, Higher = more creative")
    print("    - Default: 0.8")
    print("  • topk X - Top-k sampling (1-100)")
    print("    - Limits vocabulary to top X tokens")
    print("    - Default: 50")
    print("  • topp X - Top-p sampling (0.1-1.0)")
    print("    - Cumulative probability threshold")
    print("    - Default: None (disabled)")
    print("  • minp X - Min-p sampling (0.01-0.5)")
    print("    - Minimum probability threshold")
    print("    - Default: 0.05")
    print("\n💡 Quick presets:")
    print("  • 'fast' - Enable quantization + optimized settings")
    print("  • 'quality' - Disable quantization + quality settings")
    print("  • 'balanced' - Moderate settings for speed/quality balance")
    print()

def print_conversation_help():
    """Print conversation and natural speech help."""
    print("\n🗣️  Making Speech More Human & Conversational:")
    print("\n🎯 Key Principle: CSM gets better with conversation context!")
    print("   The more you chat, the more natural it sounds.")
    print("\n💬 Natural Conversation Tips:")
    print("  • Use conversational language: 'Hey', 'Well', 'You know'")
    print("  • Add natural hesitations: 'Um', 'Uh', 'Let me think'")
    print("  • Use contractions: 'I'm', 'don't', 'can't', 'we'll'")
    print("  • Include emotions: 'I'm excited!', 'That's frustrating'")
    print("  • Ask questions: 'What do you think?', 'Right?'")
    print("  • Use emphasis: 'That's REALLY cool!', 'I love this'")
    print("\n📝 Example Natural Phrases:")
    print("  Instead of: 'Hello. How are you today?'")
    print("  Try: 'Hey there! How's it going?'")
    print("\n  Instead of: 'I will complete the task.'")
    print("  Try: 'Sure thing! I'll get that done for you.'")
    print("\n  Instead of: 'The weather is nice.'")
    print("  Try: 'Wow, what a beautiful day! The weather's perfect.'")
    print("\n🔄 Conversation Commands:")
    print("  • 'conversation' - View current conversation history")
    print("  • 'clear' - Clear conversation history (start fresh)")
    print("  • 'natural' - Show more natural speech examples")
    print()

def print_natural_examples():
    """Print natural speech examples."""
    print("\n🎭 Natural Speech Examples:")
    print("\n😊 Friendly & Casual:")
    print("  • 'Hey! How's your day going so far?'")
    print("  • 'Oh wow, that sounds really interesting!'")
    print("  • 'I totally get what you mean.'")
    print("  • 'That's awesome! Tell me more about it.'")
    print("\n🤔 Thoughtful & Conversational:")
    print("  • 'Hmm, let me think about that for a second...'")
    print("  • 'You know what? That's a really good point.'")
    print("  • 'Well, I suppose it depends on how you look at it.'")
    print("  • 'Actually, that reminds me of something...'")
    print("\n😮 Expressive & Emotional:")
    print("  • 'No way! That's incredible!'")
    print("  • 'Oh my gosh, I can't believe that happened!'")
    print("  • 'Ugh, that must have been so frustrating.'")
    print("  • 'I'm so excited to hear about this!'")
    print("\n💡 Pro Tips:")
    print("  • The AI learns from YOUR conversation style")
    print("  • Each exchange makes the next one more natural")
    print("  • Don't be afraid to be expressive and emotional!")
    print("  • Use punctuation for natural pauses and emphasis")
    print()


def handle_settings_command(tts: TTSEngine, command: str) -> bool:
    """Handle settings-related commands. Returns True if command was handled."""
    parts = command.lower().split()

    if len(parts) == 1:
        if parts[0] == 'settings':
            print_settings_help()
            return True
        elif parts[0] == 'status':
            print(f"\n📊 Engine Status:")
            print(tts.get_status())
            return True
        elif parts[0] == 'natural':
            print_conversation_help()
            return True
        elif parts[0] == 'examples':
            print_natural_examples()
            return True
        elif parts[0] == 'conversation':
            print(f"\n💬 Conversation History:")
            history = tts.get_conversation_summary()
            print(history)
            return True
        elif parts[0] == 'clear':
            tts.clear_conversation()
            return True
        elif parts[0] == 'quantize':
            tts.apply_quantization()
            return True
        elif parts[0] == 'fast':
            tts.apply_quantization()
            tts.update_settings(temperature=0.7, top_k=30, min_p=0.1)
            print("🚀 Fast preset applied!")
            return True
        elif parts[0] == 'quality':
            if tts.quantized:
                print("⚠️  Cannot disable quantization after it's applied. Restart to use quality mode.")
            tts.update_settings(temperature=0.9, top_k=100, min_p=0.02)
            print("🎨 Quality preset applied!")
            return True
        elif parts[0] == 'balanced':
            tts.update_settings(temperature=0.8, top_k=50, min_p=0.05)
            print("⚖️  Balanced preset applied!")
            return True

    elif len(parts) == 2:
        setting, value_str = parts
        try:
            if setting == 'temp':
                value = float(value_str)
                if 0.1 <= value <= 2.0:
                    tts.update_settings(temperature=value)
                    return True
                else:
                    print("❌ Temperature must be between 0.1 and 2.0")
                    return True
            elif setting == 'topk':
                value = int(float(value_str))
                if 1 <= value <= 100:
                    tts.update_settings(top_k=value)
                    return True
                else:
                    print("❌ Top-k must be between 1 and 100")
                    return True
            elif setting == 'topp':
                value = float(value_str)
                if 0.1 <= value <= 1.0:
                    tts.update_settings(top_p=value)
                    return True
                else:
                    print("❌ Top-p must be between 0.1 and 1.0")
                    return True
            elif setting == 'minp':
                value = float(value_str)
                if 0.01 <= value <= 0.5:
                    tts.update_settings(min_p=value)
                    return True
                else:
                    print("❌ Min-p must be between 0.01 and 0.5")
                    return True
        except ValueError:
            print(f"❌ Invalid value for {setting}: {value_str}")
            return True

    return False

def main():
    """Main interactive CLI loop."""
    # Hugging Face API key
    HF_TOKEN = "*************************************"

    # Reference audio path
    REFERENCE_AUDIO = "/Users/<USER>/Documents/Dev/toni test voice/conversational_b.wav"

    print_welcome()

    # Initialize TTS engine with reference audio
    tts = TTSEngine(HF_TOKEN, REFERENCE_AUDIO)

    # Setup model
    if not tts.setup_model():
        print("❌ Failed to setup model. Exiting.")
        return

    print("🚀 Ready! Type your text below:")

    # Main interaction loop
    while True:
        try:
            # Get user input
            user_input = input("\n💬 You: ").strip()

            # Handle special commands
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            elif user_input.lower() in ['help', 'h']:
                print_help()
                continue
            elif handle_settings_command(tts, user_input):
                continue
            elif not user_input:
                print("💭 Please enter some text to speak.")
                continue

            # Generate and play speech
            print("🔄 Processing...")
            success = tts.speak(user_input)

            if success:
                print("✅ Done!")
            else:
                print("❌ Failed to generate speech. Please try again.")

        except KeyboardInterrupt:
            print("\n\n🛑 Interrupted by user.")
            print("👋 Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Unexpected error: {e}")
            print("Please try again or type 'quit' to exit.")


if __name__ == "__main__":
    main()
